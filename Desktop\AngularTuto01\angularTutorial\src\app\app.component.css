/* h1,h2{
    background-color: rgb(10, 10, 10);
    color: white;
}
button{
    background-color: rgb(10, 10, 10);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    outline: none;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
}
.counter{
    border: 2px solid white;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    text-align: center;
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 22rem;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        transform: scale(1.1);
    }
    color: rgb(246, 246, 246);
    background-color: rgb(0, 0, 0);
    font-size: 2rem;
    font-weight: bold;
} */
body{
    background-color: rgb(10, 10, 10);
    color: white;
}
h1{
    color: red;
    background-color: rgb(44, 44, 2);  
    border: 2px solid white;
}

button{
    background-color: rgb(10, 10, 10);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    outline: none;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:active{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:focus{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:visited{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:before{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        transition: all 0.5s ease;
        transform: scale(0);
    }
    &:hover:before{
        transform: scale(1);
    }
}

/* Stylish Counter Styles */
.counter-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    margin: 20px auto;
    max-width: 500px;
    position: relative;
    overflow: hidden;
}

.counter-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.counter-title {
    color: white !important;
    background: none !important;
    border: none !important;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
}

.counter-display {
    background: rgba(255, 255, 255, 0.2);
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    width: 150px;
    height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 40px;
    backdrop-filter: blur(10px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.counter-display:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.counter-value {
    font-size: 3rem;
    font-weight: 800;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.button-group {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 25px !important;
    border: none !important;
    border-radius: 15px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    cursor: pointer;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2) !important;
}

.btn-icon {
    font-size: 1.5rem;
    margin-bottom: 5px;
    transition: transform 0.3s ease;
}

.btn-text {
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-increment {
    background: linear-gradient(135deg, #4CAF50, #45a049) !important;
    color: white !important;
}

.btn-increment:hover {
    background: linear-gradient(135deg, #45a049, #3d8b40) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 25px rgba(76, 175, 80, 0.4) !important;
}

.btn-decrement {
    background: linear-gradient(135deg, #f44336, #d32f2f) !important;
    color: white !important;
}

.btn-decrement:hover {
    background: linear-gradient(135deg, #d32f2f, #b71c1c) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 25px rgba(244, 67, 54, 0.4) !important;
}

.btn-reset {
    background: linear-gradient(135deg, #FF9800, #F57C00) !important;
    color: white !important;
}

.btn-reset:hover {
    background: linear-gradient(135deg, #F57C00, #E65100) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 12px 25px rgba(255, 152, 0, 0.4) !important;
}

.btn:hover .btn-icon {
    transform: scale(1.2) rotate(10deg);
}

.btn:active {
    transform: translateY(0) scale(0.95) !important;
}

/* Ripple effect */
.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::after {
    width: 200px;
    height: 200px;
}