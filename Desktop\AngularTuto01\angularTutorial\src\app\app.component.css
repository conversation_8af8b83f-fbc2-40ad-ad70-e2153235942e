/* h1,h2{
    background-color: rgb(10, 10, 10);
    color: white;
}
button{
    background-color: rgb(10, 10, 10);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    outline: none;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
}
.counter{
    border: 2px solid white;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    text-align: center;
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-left: 22rem;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        transform: scale(1.1);
    }
    color: rgb(246, 246, 246);
    background-color: rgb(0, 0, 0);
    font-size: 2rem;
    font-weight: bold;
} */
body{
    background-color: rgb(10, 10, 10);
    color: white;
}
h1{
    color: red;
    background-color: rgb(44, 44, 2);  
    border: 2px solid white;
}

button{
    background-color: rgb(10, 10, 10);
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px;
    margin: 10px;
    cursor: pointer;
    font-size: 1.5rem;
    font-weight: bold;
    outline: none;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    transition: all 0.5s ease;
    &:hover{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:active{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:focus{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:visited{
        background-color: rgb(234, 83, 245);
        color: black;
        transform: scale(1.1);
    }
    &:before{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 5px;
        transition: all 0.5s ease;
        transform: scale(0);
    }
    &:hover:before{
        transform: scale(1);
    }   
}