<!-- Stylish Counter Component -->
<div class="counter-container">
  <h1 class="counter-title">Stylish Counter</h1>

  <div class="counter-display">
    <span class="counter-value">{{count()}}</span>
  </div>

  <div class="button-group">
    <button class="btn btn-decrement" (click)="decrement()">
      <span class="btn-icon">−</span>
      <span class="btn-text">Decrement</span>
    </button>

    <button class="btn btn-reset" (click)="reset()">
      <span class="btn-icon">↻</span>
      <span class="btn-text">Reset</span>
    </button>

    <button class="btn btn-increment" (click)="increment()">
      <span class="btn-icon">+</span>
      <span class="btn-text">Increment</span>
    </button>
  </div>
</div>

<hr style="margin: 40px 0;">

<!-- Previous Content -->
<h1 style="color: red; background-color: rgb(0, 0, 0);">If else Conditions Checking</h1>

<button (click)="display=!display">Toggle this</button>
@if(color=='white'){
<div style="background-color: rgb(255, 255, 255); color: black; height: 200px; width: 200px; border: 2px solid rgb(0, 0, 0); border-radius: 10px; box-shadow: 0 0 100px rgba(255, 255, 255, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
    <h2>Angular Js</h2>
</div>
}
@else if(color=='red'){
    <div style="background-color: rgb(163, 4, 4); color: white; height: 200px; width: 200px; border: 2px solid white; border-radius: 10px; box-shadow: 0 0 100px rgba(255, 0, 0, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
        <h2>Angular Js</h2>
    </div>
}
@else{
    <div style="background-color: rgb(0, 0, 0); color: white; height: 200px; width: 200px; border: 2px solid white; border-radius: 10px; box-shadow: 0 0 100px rgba(3, 3, 3, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
        <h2>Angular Js</h2>
    </div>
}
<br><hr>
<button (click)="color='red'">Turn Red</button>
<button (click)="color='black'">Turn Black</button>
<button (click)="color='white'">Turn White</button>

<h1>For Loop in Angulars</h1>

@for(user of users; track user){
    <h2>{{user}}</h2>
}
<hr>
<hr>
@for(student of students; track student){
    <h2>{{student.name}} - {{student.age}} - {{student.city}}</h2>
}

<h2>{{count()}}</h2>