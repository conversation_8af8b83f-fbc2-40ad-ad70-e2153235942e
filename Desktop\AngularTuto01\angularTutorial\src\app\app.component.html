<!-- <h1>Hello Your App is Running.</h1>
<h2>{{title}}</h2>
<h3>{{name}}</h3> -->
<!-- <app-log></app-log>
<app-signup></app-signup> -->
<!-- <app-signup></app-signup> -->
<!-- <app-profile></app-profile> -->
 <!-- <h1> Hello Angular19</h1> -->
<!-- <h2>{{name}}</h2> -->
<!-- <h1>{{title}}</h1> -->
<!-- <button (click)="handleOpenEven()">OpenEvent</button>
<button (click)="handleClickEvent()">Click me</button>
<app-log></app-log>
<app-profile></app-profile> -->


<!-- dependencies-> vs dev-dependencies-> -->

<!-- package.json -> project ki sari ki sari details isme store hoti hain -->
 <!-- tsconfig.json -> typescript ki sari ki sari details isme store hoti hain -->
  <!-- angular.json -> angular ki sari ki sari details isme store hoti hain -->
  
  <!-- <div>
    <h2>Counter</h2>
    <h2 class="counter">{{count}}</h2>
    <div>
        <button (click)="handleIncrement()">Increment</button>
        <button (click)="handleDecrement()">Decrement</button>
        <button (click)="handleReset()">Reset</button>
    </div>
    
  </div>


<div>
        <button (click)="handleEvent($event)">Event</button>
</div>
<div (mouseenter)="handleEvent($event)" style="background-color: rgb(212, 1, 1); border:10px solid rgb(0, 0, 0); color: white; height: 200px; width: 200px;">
</div> -->
<!-- <h1>Get input and set field value</h1>
<input type="text"value={{name}} placeholder="Enter your name" (input)="getName($event)">
<h1>this name is {{displayName}}</h1>
<br>
<br>
<hr>
<button (click)="displayname()">Click value</button>
<hr>
<button (click)="setName()">SetName</button>
<hr>
<hr> -->
<!-- <h1>Style Rules and Options</h1> -->
<h1 style="color: red; background-color: rgb(0, 0, 0);">If else Conditions Checking</h1>

<button (click)="display=!display">Toggle this</button>
@if(color=='white'){
<div style="background-color: rgb(255, 255, 255); color: black; height: 200px; width: 200px; border: 2px solid rgb(0, 0, 0); border-radius: 10px; box-shadow: 0 0 100px rgba(255, 255, 255, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
    <h2>Angular Js</h2>
</div>
}
@else if(color=='red'){
    <div style="background-color: rgb(163, 4, 4); color: white; height: 200px; width: 200px; border: 2px solid white; border-radius: 10px; box-shadow: 0 0 100px rgba(255, 0, 0, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
        <h2>Angular Js</h2>
    </div>
}
@else{
    <div style="background-color: rgb(0, 0, 0); color: white; height: 200px; width: 200px; border: 2px solid white; border-radius: 10px; box-shadow: 0 0 100px rgba(3, 3, 3, 0.5); transition: all 0.5s ease; transform: scale(1.1); margin-left: 10px;">Angular Js
        <h2>Angular Js</h2>
    </div>
}
<br><hr>
<button (click)="color='red'">Turn Red</button>
<button (click)="color='black'">Turn Black</button>
<button (click)="color='white'">Turn White</button>

<h1>For Loop in Angulars</h1>

@for(user of users; track user){
    <h2>{{user}}</h2>
}
<hr>
<hr>
@for(student of students; track student){
    <h2>{{student.name}} - {{student.age}} - {{student.city}}</h2>
}

<h2>{{count()}}</h2>