import { Component, signal } from '@angular/core';
import { RouterOutlet } from '@angular/router';
// import { LogComponent } from './log/log.component';
// import { SignupComponent } from './signup/signup.component';
// import { ProfileComponent } from './profile/profile.component';
@Component({
  selector: 'app-root',
  imports: [RouterOutlet],
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent {
  // ye sb properties hai jo bhi class ke andr hote hai containers
  // pr class ke andr vo containers bnenge unko vairable bol sakte hai
  // title = 'App on Angulars';
  // name:string = 'Angulars Tutorial';
  // data:string|number=120;
  // other:boolean=true;
  // variable:any;

  // handleClickEvent(){
  //   this.variable="sharthak";
  //   this.other=!this.other;
  //   this.data="sharthak";
  //   alert('Button clicked! Called');
  //   console.log('Button clicked! Called');
  // }
  // count=0;
  // handleIncrement(){
  //   this.count++;
  // }
  // handleDecrement(){
  //   if(this.count > 0){
  //     this.count--;
  //   } else {
  //     this.count = 0;
  //   }
  // }
  // handleReset(){
  //   this.count=0;
  // }
  // handleOpenEven(){
  //   let a=20;
  //   let b=30;
  //   let c=a+b;
  //   alert(c);
  // }
  // handleEvent(event:any){
  //   console.log('Mouse event:', event);
  //   if (event && event.target && event.target.type) {
  //     console.log(event.target.type);
  //   }
  //   console.log("Function Called");
  // }
  // name="";
  // displayName="";
  // getName(event: Event) {
  //   const inputElement = event.target as HTMLInputElement;
  //   this.name = inputElement.value;
  //   console.log(this.name);
  // }
  // displayname(){
  //   this.displayName=this.name;
  // }
  // setName(){
  //   this.name="Sarthak";
  //   this.displayName=this.name;
  // }
  display=true;
  color="red";
  users=["ABC","DEF","GHI","JKL","MNO","PQR","STU","VWX","YZA"];
  students=[
    {name:"Sarthak",age:20,city:"Delhi"},
    {name:"Rahul",age:21,city:"Mumbai"},
    {name:"Raj",age:22,city:"Chennai"}
  ]
  count = signal(10);
}
